<template>
  <div class="sse-markdown-diagnostic">
    <div class="diagnostic-header">
      <h3>🔍 SSE + Markdown 流式解析诊断工具</h3>
      <div class="controls">
        <el-button @click="startDiagnostic" :disabled="isRunning" type="primary">
          开始诊断
        </el-button>
        <el-button @click="stopDiagnostic" :disabled="!isRunning" type="danger">
          停止诊断
        </el-button>
        <el-button @click="clearLogs" type="info">
          清空日志
        </el-button>
      </div>
    </div>

    <div class="diagnostic-content">
      <!-- 实时统计 -->
      <div class="stats-panel">
        <div class="stat-item">
          <label>SSE 消息数:</label>
          <span class="stat-value">{{ sseMessageCount }}</span>
        </div>
        <div class="stat-item">
          <label>Markdown 渲染次数:</label>
          <span class="stat-value">{{ markdownRenderCount }}</span>
        </div>
        <div class="stat-item">
          <label>平均 SSE 间隔:</label>
          <span class="stat-value">{{ averageSSEInterval }}ms</span>
        </div>
        <div class="stat-item">
          <label>平均渲染耗时:</label>
          <span class="stat-value">{{ averageRenderTime }}ms</span>
        </div>
        <div class="stat-item">
          <label>渲染延迟:</label>
          <span class="stat-value" :class="{ 'delay-warning': renderDelay > 100 }">{{ renderDelay }}ms</span>
        </div>
      </div>

      <!-- 内容对比 -->
      <div class="content-comparison">
        <div class="content-panel">
          <h4>📨 SSE 原始内容 ({{ rawContent.length }} 字符)</h4>
          <div class="content-box raw-content">
            <pre>{{ rawContent }}</pre>
          </div>
        </div>
        
        <div class="content-panel">
          <h4>🎨 Markdown 渲染结果</h4>
          <div class="content-box rendered-content">
            <NativeMarkdownRenderer
              :content="rawContent"
              :is-streaming="isRunning"
              @render-count="updateRenderCount"
            />
          </div>
        </div>
      </div>

      <!-- 详细日志 -->
      <div class="logs-panel">
        <h4>📋 详细诊断日志</h4>
        <div class="logs-container">
          <div 
            v-for="(log, index) in diagnosticLogs" 
            :key="index"
            :class="['log-entry', log.type]"
          >
            <span class="log-time">{{ log.timestamp }}</span>
            <span class="log-type">{{ log.type.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NativeMarkdownRenderer from './NativeMarkdownRenderer.vue'

export default {
  name: 'SSEMarkdownDiagnostic',
  components: {
    NativeMarkdownRenderer
  },
  data() {
    return {
      isRunning: false,
      rawContent: '',
      sseMessageCount: 0,
      markdownRenderCount: 0,
      diagnosticLogs: [],
      sseTimestamps: [],
      renderTimestamps: [],
      renderTimes: [],
      lastSSETime: null,
      lastRenderTime: null,
      simulationTimer: null,
      
      // 测试用的 Markdown 内容
      testMarkdown: `# 流式 Markdown 测试

## 这是一个测试标题

**粗体文本** 和 *斜体文本* 的测试。

### 代码块测试
\`\`\`javascript
function test() {
  console.log("Hello World");
  return true;
}
\`\`\`

### 列表测试
- 第一项
- 第二项
- 第三项

### 链接测试
[GitHub](https://github.com) 是一个很好的代码托管平台。

### 引用测试
> 这是一个引用块
> 用来测试流式渲染效果

### 表格测试
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

### 更多内容
这里有更多的内容来测试流式输出的效果。包含各种 **格式** 和 *样式*。

\`inline code\` 也需要测试。

最后一段内容，用来完成整个测试。`
    }
  },
  computed: {
    averageSSEInterval() {
      if (this.sseTimestamps.length < 2) return 0
      const intervals = []
      for (let i = 1; i < this.sseTimestamps.length; i++) {
        intervals.push(this.sseTimestamps[i] - this.sseTimestamps[i - 1])
      }
      return Math.round(intervals.reduce((a, b) => a + b, 0) / intervals.length)
    },
    
    averageRenderTime() {
      if (this.renderTimes.length === 0) return 0
      return Math.round(this.renderTimes.reduce((a, b) => a + b, 0) / this.renderTimes.length)
    },
    
    renderDelay() {
      if (!this.lastSSETime || !this.lastRenderTime) return 0
      return Math.max(0, this.lastRenderTime - this.lastSSETime)
    }
  },
  methods: {
    startDiagnostic() {
      this.isRunning = true
      this.rawContent = ''
      this.sseMessageCount = 0
      this.markdownRenderCount = 0
      this.sseTimestamps = []
      this.renderTimestamps = []
      this.renderTimes = []
      this.lastSSETime = null
      this.lastRenderTime = null
      
      this.addLog('info', '🚀 开始 SSE + Markdown 诊断测试')
      
      // 模拟 SSE 流式数据接收
      this.simulateSSEStream()
    },
    
    stopDiagnostic() {
      this.isRunning = false
      if (this.simulationTimer) {
        clearInterval(this.simulationTimer)
        this.simulationTimer = null
      }
      this.addLog('info', '🛑 诊断测试已停止')
    },
    
    simulateSSEStream() {
      let currentIndex = 0
      const content = this.testMarkdown
      
      this.simulationTimer = setInterval(() => {
        if (currentIndex >= content.length) {
          this.stopDiagnostic()
          return
        }
        
        // 模拟 SSE 数据块大小（1-5个字符）
        const chunkSize = Math.floor(Math.random() * 5) + 1
        const endIndex = Math.min(currentIndex + chunkSize, content.length)
        const chunk = content.substring(currentIndex, endIndex)
        
        // 模拟 SSE 消息接收
        this.simulateSSEMessage(chunk)
        
        currentIndex = endIndex
        
      }, this.getRandomSSEInterval()) // 随机间隔模拟真实 SSE
    },
    
    getRandomSSEInterval() {
      // 模拟真实 SSE 的不规律间隔：50-200ms
      return Math.floor(Math.random() * 150) + 50
    },
    
    simulateSSEMessage(chunk) {
      const now = performance.now()
      this.lastSSETime = now
      this.sseTimestamps.push(now)
      this.sseMessageCount++
      
      // 追加内容
      this.rawContent += chunk
      
      this.addLog('sse', `📨 SSE消息 #${this.sseMessageCount}: "${chunk}" (${chunk.length} 字符)`)
      
      // 触发 Markdown 渲染（模拟 Vuex store 的行为）
      this.$nextTick(() => {
        this.triggerMarkdownRender()
      })
    },
    
    triggerMarkdownRender() {
      const renderStart = performance.now()
      this.lastRenderTime = renderStart
      
      // 强制触发渲染
      this.$forceUpdate()
      
      this.$nextTick(() => {
        const renderEnd = performance.now()
        const renderTime = renderEnd - renderStart
        this.renderTimes.push(renderTime)
        this.renderTimestamps.push(renderEnd)
        
        this.addLog('render', `🎨 Markdown渲染完成: 耗时 ${renderTime.toFixed(2)}ms, 延迟 ${this.renderDelay.toFixed(2)}ms`)
      })
    },
    
    updateRenderCount(count) {
      this.markdownRenderCount = count
    },
    
    addLog(type, message) {
      const timestamp = new Date().toLocaleTimeString() + '.' + Date.now().toString().slice(-3)
      this.diagnosticLogs.unshift({
        type,
        message,
        timestamp
      })
      
      // 限制日志数量
      if (this.diagnosticLogs.length > 100) {
        this.diagnosticLogs = this.diagnosticLogs.slice(0, 100)
      }
    },
    
    clearLogs() {
      this.diagnosticLogs = []
    }
  },
  
  beforeDestroy() {
    this.stopDiagnostic()
  }
}
</script>

<style scoped>
.sse-markdown-diagnostic {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.diagnostic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.diagnostic-header h3 {
  margin: 0;
  color: #333;
}

.controls {
  display: flex;
  gap: 10px;
}

.stats-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-item label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.delay-warning {
  color: #f56c6c !important;
}

.content-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.content-panel h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.content-box {
  height: 300px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow-y: auto;
  background: white;
}

.raw-content {
  padding: 15px;
}

.raw-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.rendered-content {
  padding: 15px;
}

.logs-panel h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.logs-container {
  height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 10px;
}

.log-entry {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.log-time {
  color: #999;
  min-width: 80px;
}

.log-type {
  min-width: 60px;
  font-weight: bold;
}

.log-entry.sse .log-type {
  color: #67c23a;
}

.log-entry.render .log-type {
  color: #409eff;
}

.log-entry.info .log-type {
  color: #909399;
}

.log-message {
  flex: 1;
  color: #333;
}
</style>
