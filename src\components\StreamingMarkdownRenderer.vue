<template>
  <div class="streaming-markdown-renderer">
    <div v-html="renderedContent" class="markdown-content"></div>
  </div>
</template>

<script>
// 尝试多种方式导入 MarkdownIt
let MarkdownIt
try {
  MarkdownIt = require('markdown-it')
} catch (e) {
  try {
    MarkdownIt = window.markdownit
  } catch (e2) {
    console.error('无法加载 MarkdownIt:', e, e2)
  }
}

/**
 * 专门为流式输出优化的 Markdown 渲染器
 * 基于测试页面中成功的实现，确保流式输出时能正确解析
 */
export default {
  name: 'StreamingMarkdownRenderer',
  props: {
    // Markdown内容
    content: {
      type: String,
      default: ''
    },
    // 是否启用HTML标签
    enableHtml: {
      type: Boolean,
      default: true
    },
    // 是否启用换行转换
    enableBreaks: {
      type: Boolean,
      default: true
    },
    // 是否启用链接识别
    enableLinkify: {
      type: Boolean,
      default: true
    },
    // 是否启用排版优化
    enableTypographer: {
      type: Boolean,
      default: true
    },
    // 是否正在流式输出
    isStreaming: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      md: null,
      renderCount: 0,
      forceUpdateTimer: null,
      lastContent: '',
      lastRenderTime: 0
    }
  },
  computed: {
    // 渲染后的Markdown内容
    renderedContent() {
      if (!this.md || !this.content) {
        return ''
      }

      try {
        const startTime = Date.now()
        const result = this.md.render(this.content)
        const renderTime = Date.now() - startTime

        // 更新渲染统计
        this.renderCount++
        this.lastRenderTime = renderTime

        // 流式状态下的调试信息
        if (this.isStreaming) {
          console.log(`🔄 StreamingMarkdown渲染: ${this.content.length} 字符, 耗时 ${renderTime}ms, 第${this.renderCount}次`)
        }

        return result
      } catch (error) {
        console.error('❌ StreamingMarkdown渲染失败:', error)
        return `<p style="color: red;">渲染错误: ${error.message}</p>`
      }
    }
  },
  created() {
    this.initMarkdown()
  },

  mounted() {
    // 组件挂载后开始监听流式状态
    this.setupStreamingWatch()
  },

  beforeDestroy() {
    // 清理定时器
    this.cleanup()
  },

  watch: {
    // 监听内容变化
    content: {
      handler(newContent, oldContent) {
        if (newContent !== oldContent && this.isStreaming) {
          // 流式状态下，立即强制更新
          this.$nextTick(() => {
            this.$forceUpdate()
          })
        }
      },
      immediate: true
    },

    // 监听流式状态变化
    isStreaming: {
      handler(newVal, oldVal) {
        if (newVal && !oldVal) {
          // 开始流式输出
          this.startForceUpdateTimer()
        } else if (!newVal && oldVal) {
          // 结束流式输出
          this.stopForceUpdateTimer()
        }
      }
    },

    // 监听配置变化，重新初始化
    enableHtml() {
      this.initMarkdown()
    },
    enableBreaks() {
      this.initMarkdown()
    },
    enableLinkify() {
      this.initMarkdown()
    },
    enableTypographer() {
      this.initMarkdown()
    }
  },
  methods: {
    // 初始化 Markdown-it（采用测试页面中成功的配置）
    initMarkdown() {
      console.log('🔧 StreamingMarkdownRenderer: 开始初始化 Markdown-it')

      try {
        // 尝试多种方式创建 MarkdownIt 实例
        if (MarkdownIt) {
          this.md = new MarkdownIt({
            html: this.enableHtml,
            xhtmlOut: false,
            breaks: this.enableBreaks,
            linkify: this.enableLinkify,
            typographer: this.enableTypographer
          })
          console.log('✅ StreamingMarkdownRenderer: 使用导入的 MarkdownIt 初始化成功')
        } else if (window.markdownit) {
          this.md = window.markdownit({
            html: this.enableHtml,
            xhtmlOut: false,
            breaks: this.enableBreaks,
            linkify: this.enableLinkify,
            typographer: this.enableTypographer
          })
          console.log('✅ StreamingMarkdownRenderer: 使用全局 markdownit 初始化成功')
        } else {
          throw new Error('MarkdownIt 库未找到')
        }

      } catch (error) {
        console.error('❌ StreamingMarkdownRenderer: Markdown-it 初始化失败:', error)

        // 尝试最基础的后备方案
        try {
          if (window.markdownit) {
            this.md = window.markdownit({
              html: true,
              breaks: true,
              linkify: true
            })
            console.log('⚠️ StreamingMarkdownRenderer: 使用后备方案初始化成功')
          } else {
            this.md = null
            console.error('❌ StreamingMarkdownRenderer: 所有初始化方案都失败')
          }
        } catch (fallbackError) {
          console.error('❌ StreamingMarkdownRenderer: 后备初始化也失败:', fallbackError)
          this.md = null
        }
      }
    },

    // 设置流式监听
    setupStreamingWatch() {
      // 这里可以添加额外的流式监听逻辑
      console.log('🔧 StreamingMarkdownRenderer: 设置流式监听')
    },

    // 开始强制更新定时器
    startForceUpdateTimer() {
      this.stopForceUpdateTimer() // 先清理旧的定时器

      console.log('🔄 StreamingMarkdownRenderer: 启动强制更新定时器')
      this.forceUpdateTimer = setInterval(() => {
        if (this.isStreaming) {
          this.$forceUpdate()
        }
      }, 100) // 每100ms强制更新一次
    },

    // 停止强制更新定时器
    stopForceUpdateTimer() {
      if (this.forceUpdateTimer) {
        console.log('🛑 StreamingMarkdownRenderer: 停止强制更新定时器')
        clearInterval(this.forceUpdateTimer)
        this.forceUpdateTimer = null
      }
    },

    // 清理资源
    cleanup() {
      this.stopForceUpdateTimer()
    }
  }
}
</script>

<style scoped>
/* 流式 Markdown 渲染器样式 */
.streaming-markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary, #333);
  word-wrap: break-word;
  font-size: 14px;
}

/* 标题样式 */
.streaming-markdown-renderer >>> h1,
.streaming-markdown-renderer >>> h2,
.streaming-markdown-renderer >>> h3,
.streaming-markdown-renderer >>> h4,
.streaming-markdown-renderer >>> h5,
.streaming-markdown-renderer >>> h6 {
  color: #333;
  margin: 16px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
}

.streaming-markdown-renderer >>> h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.streaming-markdown-renderer >>> h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 6px;
}

.streaming-markdown-renderer >>> h3 {
  font-size: 1.3em;
  color: #409eff;
}

.streaming-markdown-renderer >>> h4 {
  font-size: 1.1em;
  color: #606266;
}

/* 段落样式 */
.streaming-markdown-renderer >>> p {
  margin: 12px 0;
  line-height: 1.7;
}

/* 列表样式 */
.streaming-markdown-renderer >>> ul,
.streaming-markdown-renderer >>> ol {
  margin: 12px 0;
  padding-left: 20px;
}

.streaming-markdown-renderer >>> li {
  margin: 6px 0;
  line-height: 1.6;
}

/* 文本格式 */
.streaming-markdown-renderer >>> strong {
  font-weight: 700;
  color: #409eff;
}

.streaming-markdown-renderer >>> em {
  font-style: italic;
  color: #666;
}

.streaming-markdown-renderer >>> del {
  text-decoration: line-through;
  color: #999;
}

/* 代码样式 */
.streaming-markdown-renderer >>> code {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
}

.streaming-markdown-renderer >>> pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.streaming-markdown-renderer >>> pre code {
  background: none;
  color: #333;
  padding: 0;
  border-radius: 0;
}

/* 引用样式 */
.streaming-markdown-renderer >>> blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #409eff;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #666;
}

.streaming-markdown-renderer >>> blockquote p {
  margin: 0;
}

/* 表格样式 */
.streaming-markdown-renderer >>> table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.streaming-markdown-renderer >>> th,
.streaming-markdown-renderer >>> td {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
}

.streaming-markdown-renderer >>> th {
  background: rgba(64, 158, 255, 0.1);
  font-weight: 600;
  color: #409eff;
}

.streaming-markdown-renderer >>> tr:last-child td {
  border-bottom: none;
}

/* 链接样式 */
.streaming-markdown-renderer >>> a {
  color: #409eff;
  text-decoration: none;
}

.streaming-markdown-renderer >>> a:hover {
  text-decoration: underline;
}

/* 分隔线样式 */
.streaming-markdown-renderer >>> hr {
  border: none;
  border-top: 2px solid #eee;
  margin: 20px 0;
}

/* 图片样式 */
.streaming-markdown-renderer >>> img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 8px 0;
}
</style>
