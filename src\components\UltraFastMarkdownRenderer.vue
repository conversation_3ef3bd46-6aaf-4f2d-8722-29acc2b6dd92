<template>
  <div class="ultra-fast-markdown-renderer" ref="container">
    <!-- 使用 v-html 直接渲染，避免 computed 的延迟 -->
    <div 
      v-html="currentRenderedContent" 
      class="markdown-content"
      :key="forceUpdateKey"
    ></div>
  </div>
</template>

<script>
// 尝试多种方式导入 MarkdownIt
let MarkdownIt
try {
  MarkdownIt = require('markdown-it')
} catch (e) {
  try {
    MarkdownIt = window.markdownit
  } catch (e2) {
    console.error('无法加载 MarkdownIt:', e, e2)
  }
}

/**
 * 超高速流式 Markdown 渲染器
 * 采用最激进的优化策略，确保实时渲染
 */
export default {
  name: 'UltraFastMarkdownRenderer',
  props: {
    content: {
      type: String,
      default: ''
    },
    enableHtml: {
      type: Boolean,
      default: true
    },
    enableBreaks: {
      type: Boolean,
      default: true
    },
    enableLinkify: {
      type: Boolean,
      default: true
    },
    enableTypographer: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    isStreaming: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      md: null,
      currentRenderedContent: '',
      forceUpdateKey: 0,
      renderCount: 0,
      lastContent: '',
      renderTimer: null,
      pendingContent: null
    }
  },
  created() {
    this.initMarkdown()
    this.setupRenderLoop()
  },
  mounted() {
    // 立即渲染一次
    this.renderContent()
    
    // 监听全局事件
    if (this.$bus) {
      this.$bus.$on('force-markdown-update', this.handleForceUpdate)
      this.$bus.$on('force-markdown-update-delayed', this.handleForceUpdate)
    }
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    // 内容变化时立即标记需要渲染
    content: {
      handler(newContent) {
        this.pendingContent = newContent
        if (this.isStreaming) {
          // 流式状态下立即渲染
          this.renderContent()
        }
      },
      immediate: true
    },
    
    // 流式状态变化
    isStreaming: {
      handler(newVal) {
        if (newVal) {
          this.setupRenderLoop()
        } else {
          this.stopRenderLoop()
          // 最后一次渲染
          this.renderContent()
        }
      }
    }
  },
  methods: {
    // 初始化 Markdown
    initMarkdown() {
      try {
        if (MarkdownIt) {
          this.md = new MarkdownIt({
            html: this.enableHtml,
            xhtmlOut: false,
            breaks: this.enableBreaks,
            linkify: this.enableLinkify,
            typographer: this.enableTypographer
          })
        } else if (window.markdownit) {
          this.md = window.markdownit({
            html: this.enableHtml,
            xhtmlOut: false,
            breaks: this.enableBreaks,
            linkify: this.enableLinkify,
            typographer: this.enableTypographer
          })
        } else {
          throw new Error('MarkdownIt 库未找到')
        }
        console.log('✅ UltraFastMarkdownRenderer: Markdown-it 初始化成功')
      } catch (error) {
        console.error('❌ UltraFastMarkdownRenderer: 初始化失败:', error)
        this.md = null
      }
    },
    
    // 设置渲染循环
    setupRenderLoop() {
      this.stopRenderLoop()
      
      // 使用 requestAnimationFrame 获得最高的渲染频率
      const renderLoop = () => {
        if (this.isStreaming) {
          this.renderContent()
          this.renderTimer = requestAnimationFrame(renderLoop)
        }
      }
      
      this.renderTimer = requestAnimationFrame(renderLoop)
    },
    
    // 停止渲染循环
    stopRenderLoop() {
      if (this.renderTimer) {
        cancelAnimationFrame(this.renderTimer)
        this.renderTimer = null
      }
    },
    
    // 核心渲染方法
    renderContent() {
      const content = this.pendingContent !== null ? this.pendingContent : this.content
      
      // 如果内容没有变化，跳过渲染
      if (content === this.lastContent && this.currentRenderedContent) {
        return
      }
      
      if (!this.md) {
        this.currentRenderedContent = content ? content.replace(/\n/g, '<br>') : ''
        return
      }
      
      try {
        const startTime = performance.now()
        
        // 直接渲染
        const rendered = this.md.render(content || '')
        
        // 立即更新
        this.currentRenderedContent = rendered
        this.lastContent = content
        this.renderCount++
        this.forceUpdateKey++
        
        // 清除待处理内容
        this.pendingContent = null
        
        const renderTime = performance.now() - startTime
        
        if (this.isStreaming) {
          console.log(`🚀 UltraFast渲染: ${content?.length || 0} 字符, 耗时 ${renderTime.toFixed(2)}ms, 第${this.renderCount}次`)
        }
        
        // 发射事件
        this.$emit('render-count', this.renderCount)
        
        // 强制 DOM 更新
        this.$nextTick(() => {
          this.$forceUpdate()
        })
        
      } catch (error) {
        console.error('❌ UltraFast渲染失败:', error)
        this.currentRenderedContent = `<p style="color: red;">渲染错误: ${error.message}</p>`
      }
    },
    
    // 处理强制更新事件
    handleForceUpdate(eventData) {
      if (this.isStreaming) {
        this.renderContent()
      }
    },
    
    // 清理资源
    cleanup() {
      this.stopRenderLoop()
      
      if (this.$bus) {
        this.$bus.$off('force-markdown-update', this.handleForceUpdate)
        this.$bus.$off('force-markdown-update-delayed', this.handleForceUpdate)
      }
    }
  }
}
</script>

<style scoped>
.ultra-fast-markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary, #333);
  word-wrap: break-word;
  font-size: 14px;
}

/* 标题样式 */
.ultra-fast-markdown-renderer >>> h1,
.ultra-fast-markdown-renderer >>> h2,
.ultra-fast-markdown-renderer >>> h3,
.ultra-fast-markdown-renderer >>> h4,
.ultra-fast-markdown-renderer >>> h5,
.ultra-fast-markdown-renderer >>> h6 {
  color: #333;
  margin: 16px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
}

.ultra-fast-markdown-renderer >>> h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.ultra-fast-markdown-renderer >>> h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 6px;
}

.ultra-fast-markdown-renderer >>> h3 {
  font-size: 1.3em;
  color: #409eff;
}

/* 段落样式 */
.ultra-fast-markdown-renderer >>> p {
  margin: 12px 0;
  line-height: 1.7;
}

/* 列表样式 */
.ultra-fast-markdown-renderer >>> ul,
.ultra-fast-markdown-renderer >>> ol {
  margin: 12px 0;
  padding-left: 20px;
}

.ultra-fast-markdown-renderer >>> li {
  margin: 6px 0;
  line-height: 1.6;
}

/* 文本格式 */
.ultra-fast-markdown-renderer >>> strong {
  font-weight: 700;
  color: #409eff;
}

.ultra-fast-markdown-renderer >>> em {
  font-style: italic;
  color: #666;
}

/* 代码样式 */
.ultra-fast-markdown-renderer >>> code {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
}

.ultra-fast-markdown-renderer >>> pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.ultra-fast-markdown-renderer >>> pre code {
  background: none;
  color: #333;
  padding: 0;
  border-radius: 0;
}

/* 引用样式 */
.ultra-fast-markdown-renderer >>> blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #409eff;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #666;
}

/* 表格样式 */
.ultra-fast-markdown-renderer >>> table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.ultra-fast-markdown-renderer >>> th,
.ultra-fast-markdown-renderer >>> td {
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
}

.ultra-fast-markdown-renderer >>> th {
  background: rgba(64, 158, 255, 0.1);
  font-weight: 600;
  color: #409eff;
}

/* 链接样式 */
.ultra-fast-markdown-renderer >>> a {
  color: #409eff;
  text-decoration: none;
}

.ultra-fast-markdown-renderer >>> a:hover {
  text-decoration: underline;
}
</style>
